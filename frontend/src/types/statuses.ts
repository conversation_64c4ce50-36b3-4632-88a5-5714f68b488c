import { ErrorType, getErrorEmoji } from './errors'
import type { Source } from '@/domain/types'
import { PlainDateTime, getNow } from '@/libs/temporal'

export enum ScrapeStatus {
  CONFIGURED = 'CONFIGURED',
  DELETED = 'DELETED',
  DISABLED = 'DISABLED',
  FAILED = 'FAILED',
  FINISHED = 'FINISHED',
  MANUALLY_BLOCKED = 'MANUALLY_BLOCKED',
  MANUALLY_UNBLOCKED = 'MANUALLY_UNBLOCKED',
  SCHEDULED = 'SCHEDULED',
  STARTED = 'STARTED',
  STOPPED = 'STOPPED',
  UNCONFIGURED = 'UNCONFIGURED',
}

export const IN_PROGRESS_STATES = ['STARTED', 'SCHEDULED']
export const STALE_THRESHOLD_MS = 60 * 60 * 1000 // 60 minutes

export enum ReportStatus {
  CONVERTED = 'CONVERTED',
  DELETED = 'DELETED',
  FAILED = 'FAILED',
  PENDING = 'PENDING',
  NONE = 'NONE',
}

const SCRAPE_STATUS_SYMBOLS: Record<ScrapeStatus, string> = {
  [ScrapeStatus.CONFIGURED]: '',
  [ScrapeStatus.DELETED]: '',
  [ScrapeStatus.DISABLED]: '',
  [ScrapeStatus.FAILED]: '',
  [ScrapeStatus.FINISHED]: '',
  [ScrapeStatus.MANUALLY_BLOCKED]: '',
  [ScrapeStatus.MANUALLY_UNBLOCKED]: '',
  [ScrapeStatus.SCHEDULED]: '',
  [ScrapeStatus.STARTED]: '',
  [ScrapeStatus.STOPPED]: '',
  [ScrapeStatus.UNCONFIGURED]: '',
}

const SCRAPE_STATUS_SYMBOL_CLASSES: Record<ScrapeStatus, string> = {
  [ScrapeStatus.CONFIGURED]: 'wrench white fas fa-wrench',
  [ScrapeStatus.STARTED]: 'question white',
  [ScrapeStatus.FINISHED]: '',
  [ScrapeStatus.FAILED]: '',
  [ScrapeStatus.SCHEDULED]: '',
  [ScrapeStatus.DISABLED]: '',
  [ScrapeStatus.STOPPED]: '',
  [ScrapeStatus.DELETED]: '',
  [ScrapeStatus.MANUALLY_BLOCKED]: '',
  [ScrapeStatus.MANUALLY_UNBLOCKED]: '',
  [ScrapeStatus.UNCONFIGURED]: '',
}

const REPORT_STATUS_SYMBOL_CLASSES: Record<ReportStatus, string> = {
  [ReportStatus.CONVERTED]: 'checkmark',
  [ReportStatus.FAILED]: 'x red',
  [ReportStatus.DELETED]: 'x red',
  [ReportStatus.NONE]: '',
  [ReportStatus.PENDING]: 'question yellow',
}

export interface StatusInfo {
  scrapeStatus: ScrapeStatus
  reportStatus?: ReportStatus
  errorType?: ErrorType
  data: {
    source: Source
    organizationId: string
    organizationName: string
    lastOperationId: string | null
    createdAt: PlainDateTime
    updatedAt: PlainDateTime
    consecutiveFailedScrapeCount: number
    lastSuccessTimestamp: PlainDateTime | null
    lastFailTimestamp: PlainDateTime | null
    lastFailReason: string | null
    userId: string
    lastOrigin: string | null
    userEmail: string
    version: number
    reports: Array<Report | ReportPlaceholder>
  }
}

export interface Report {
  id: number
  studio_id: number
  organization_id: string | null
  source: string
  file_path_raw: string
  original_name: string
  portal: string
  date_from: string | null
  date_to: string | null
  no_data: boolean
  upload_date: string
  state: string
  upload_type: string
}

export interface ReportPlaceholder {
  date_from: string
  date_to: string
  state: 'NOT_UPLOADED_YET'
}

export type StatusInfoWithoutData = Omit<StatusInfo, 'data'>

export const getReportStatusClass = (statusInfo: StatusInfo): string => {
  if (!statusInfo.reportStatus || statusInfo.reportStatus === ReportStatus.NONE) {
    return ''
  }

  return statusInfo.reportStatus.toLowerCase()
}

export const shouldPulse = (scrapeStatus: ScrapeStatus): boolean => {
  return scrapeStatus === ScrapeStatus.STARTED
}

const shouldModifyForDebug = (timestamp: PlainDateTime): boolean => {
  // Use seconds for deterministic "randomness" - 2% chance
  return timestamp.second % 50 < 1 // 1 out of 50 = 2%
}

export const getStatusSymbol = (statusInfo: StatusInfo): string => {

  if (statusInfo.errorType) {
    return getErrorEmoji(statusInfo.errorType)
  }

  let lastSuccessTimestamp = statusInfo.data.lastSuccessTimestamp

  // deterministically modify statusInfo.data.lastSuccessTimestamp for debug purposes
  if (lastSuccessTimestamp && shouldModifyForDebug(lastSuccessTimestamp)) {
    // Use minute to decide between 24h and 48h subtraction
    const hoursToSubtract = lastSuccessTimestamp.minute % 2 === 0 ? 24 : 48
    lastSuccessTimestamp = lastSuccessTimestamp.subtract({ hours: hoursToSubtract })
  }

  // for successes than happen more than 24 hours ago, show a number of hours since last success as a symbol
  if (statusInfo.scrapeStatus === ScrapeStatus.FINISHED && lastSuccessTimestamp &&
    PlainDateTime.compare(lastSuccessTimestamp, getNow().subtract({ hours: 24 })) < 0
  ) {
    const hoursSinceLastSuccess = getNow().since(lastSuccessTimestamp, { largestUnit: 'hour' }).hours

    if (hoursSinceLastSuccess >= 72) {
      const daysSinceLastSuccess = Math.floor(hoursSinceLastSuccess / 24)
      return daysSinceLastSuccess.toString() + "d"
    } else {
      return hoursSinceLastSuccess.toString() + "h"
    }
  }

  return SCRAPE_STATUS_SYMBOLS[statusInfo.scrapeStatus]
}

export const getStatusSymbolClass = (statusInfo: StatusInfo): string => {
  if (statusInfo.errorType) {
    return 'error-emoji'
  }

  let lastSuccessTimestamp = statusInfo.data.lastSuccessTimestamp

  // deterministically modify statusInfo.data.lastSuccessTimestamp for debug purposes
  if (lastSuccessTimestamp && shouldModifyForDebug(lastSuccessTimestamp)) {
    // Use minute to decide between 24h and 48h subtraction
    const hoursToSubtract = lastSuccessTimestamp.minute % 2 === 0 ? 24 : 48
    lastSuccessTimestamp = lastSuccessTimestamp.subtract({ hours: hoursToSubtract })
  }

  // for successes than happen more than 24 hours ago, return special class for hours display
  if (statusInfo.scrapeStatus === ScrapeStatus.FINISHED && lastSuccessTimestamp &&
    PlainDateTime.compare(lastSuccessTimestamp, getNow().subtract({ hours: 24 })) < 0
  ) {
    const hoursSinceLastSuccess = getNow().since(lastSuccessTimestamp, { largestUnit: 'hour' }).hours

    if (hoursSinceLastSuccess >= 72) {
      return 'hours-since-success-red'
    } else if (hoursSinceLastSuccess >= 48) {
      return 'hours-since-success-yellow'
    } else {
      return 'hours-since-success-white'
    }
  }

  if (statusInfo.reportStatus) {
    return REPORT_STATUS_SYMBOL_CLASSES[statusInfo.reportStatus]
  }

  return SCRAPE_STATUS_SYMBOL_CLASSES[statusInfo.scrapeStatus]
}
