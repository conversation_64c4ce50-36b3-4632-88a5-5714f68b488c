<template>
  <span v-if="symbol || symbolClass" class="symbol" :class="symbolClass">
    {{ symbol }}
  </span>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { getStatusSymbol, getStatusSymbolClass } from '@/types/statuses'
import type { StatusInfo } from '@/types/statuses'

const props = defineProps<{
  statusInfo: StatusInfo
}>()

const symbol = computed((): string => {
  return getStatusSymbol(props.statusInfo)
})

const symbolClass = computed((): string => {
  return getStatusSymbolClass(props.statusInfo)
})
</script>

<style scoped>
.symbol {
  color: white;
  font-weight: bold;
  font-size: clamp(8px, 4vw, 16px);
  line-height: 1;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: var(--z-index-status-symbol);
}

.symbol.checkmark {
  font-size: 18px;
}

.symbol.question {
  font-size: 22px;
  font-weight: 900;
  text-shadow:
    -1px -1px 0 #000,
    1px -1px 0 #000,
    -1px 1px 0 #000,
    1px 1px 0 #000;
  animation: pulse 2s infinite;
}

.symbol.x {
  font-size: 20px;
  font-weight: 900;
  text-shadow:
    -1px -1px 0 #000,
    1px -1px 0 #000,
    -1px 1px 0 #000,
    1px 1px 0 #000;
}

.symbol.white {
  color: white;
}

.symbol.yellow {
  color: #ffd700;
}

.symbol.red {
  color: #ff5630;
}

.symbol.error-emoji {
  padding-top: 0.1rem;
  font-size: 18px;
  line-height: 1;
  text-shadow:
    -1px -1px 0 #000,
    1px -1px 0 #000,
    -1px 1px 0 #000,
    1px 1px 0 #000;
}

.symbol.wrench {
  filter: drop-shadow(3px 3px 3px black);
}

.symbol.hours-since-success {
  font-size: clamp(8px, 4vw, 14px);
  font-weight: 900;
  color: #ffa500;
  text-shadow:
    -1px -1px 0 #000,
    1px -1px 0 #000,
    -1px 1px 0 #000,
    1px 1px 0 #000;
  margin: 0;
  padding: 0;
  display: block;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

@keyframes pulse {
  0% {
    transform: scale(0.9);
  }

  50% {
    transform: scale(1);
  }

  100% {
    transform: scale(0.9);
  }
}

@keyframes strong-pulse {
  0% {
    transform: scale(0.9);
  }

  50% {
    transform: scale(1.3);
  }

  100% {
    transform: scale(0.9);
  }
}
</style>
